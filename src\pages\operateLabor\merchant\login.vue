<template>
  <div
    :style="{
      display: 'flex',
      fontFamily: 'sans-serif',
      backgroundColor: '#F7F8FA',
      height: '100vh'
    }"
  >
    <slogo />

    <!-- Right Panel -->
    <div
      :style="{
        flex: '1',
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'center',
        alignItems: 'center',
        position: 'relative'
      }"
    >
      <div
        :style="{
          width: '420px',
          padding: '40px',
          backgroundColor: 'white',
          borderRadius: '8px',
          boxShadow: '0 10px 40px rgba(0,0,0,0.05)'
        }"
      >
        <h2
          :style="{
            fontSize: '28px',
            fontWeight: 'bold',
            color: '#333',
            textAlign: 'center'
          }"
        >
          登录
        </h2>

        <div
          :style="{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'baseline',
            marginBottom: '25px'
          }"
        >
          <span :style="{ fontSize: '18px', color: '#333' }">密码登录</span>
          <a
            @click="switchLoginType"
            :style="{
              fontSize: '14px',
              color: 'var(--o-primary-color)',
              cursor: 'pointer',
              textDecoration: 'none'
            }"
            >切换验证码登录</a
          >
        </div>

        <el-form :model="form" ref="form" :rules="rules">
          <el-form-item prop="account">
            <el-input
              v-model="form.account"
              placeholder="请输入手机号"
              :style="{ width: '100%' }"
            ></el-input>
          </el-form-item>
          <el-form-item prop="password">
            <el-input
              v-model="form.password"
              type="password"
              placeholder="请输入密码"
            ></el-input>
          </el-form-item>
          <el-form-item prop="captcha">
            <Captcha v-model="form.captcha" />
          </el-form-item>
          <el-form-item>
            <el-button
              type="primary"
              @click="handleLogin"
              :style="{
                width: '100%',
                fontSize: '16px',
                padding: '12px'
              }"
            >
              登录
            </el-button>
          </el-form-item>
        </el-form>

        <!-- <div style="text-align: right; margin-top: 20px">
          <a
            @click="goToForgotPassword"
            :style="{
              color: '#666',
              fontSize: '14px',
              textDecoration: 'none',
              cursor: 'pointer'
            }"
            >找回密码</a
          >
        </div> -->
      </div>
    </div>
  </div>
</template>

<script>
import Slogo from './slogo.vue'
import Captcha from './captcha.vue'
import makeClient from 'kit/services/operateLabor/makeClient'
import { setToken } from 'kit/helpers/token'
import handleError from 'kit/helpers/handleError'
import { setCustomerUserProfile, clearDomainRelatedData, isDomainInfoValid } from './context'
const client = makeClient()

export default {
  name: 'Login',
  components: {
    Slogo,
    Captcha
  },
  data() {
    return {
      form: {
        account: '',
        password: '',
        captcha: {
          token: '',
          value: ''
        },
        type: 'CUSTOMER',
        smsLogin: false
      },
      rules: {
        account: [{ required: true, message: '请输入手机号', trigger: 'blur' }],
        password: [{ required: true, message: '请输入密码', trigger: 'blur' }],
        captcha: [
          { required: true, message: '请输入图形验证码', trigger: 'blur' }
        ]
      }
    }
  },
  async created() {
    // 检查当前域名信息是否有效，如果无效则清理所有相关数据
    if (!isDomainInfoValid()) {
      clearDomainRelatedData()
    }

    var domain = window.location.host
    if (domain.includes('localhost')) {
      domain = '156-dev.olading.com'
    }
    const [err1, r1] = await client.getDomainInfo({
      body: {
        domain
      }
    })
    if (err1) {
      handleError(err1)
      return
    }

    localStorage.setItem('domainInfo', JSON.stringify(r1.data))
  },
  methods: {
    async handleLogin() {
      const valid = await this.$refs.form.validate()
      if (!valid) {
        return
      }

      const [err,r] = await client.login({
        body: {
          account: this.form.account,
          password: this.form.password,
          captchaToken: this.form.captcha.token,
          captchaAnswer: this.form.captcha.value,
          type: this.form.type
        }
      })
      if (err) {
        handleError(err)
        return
      }

      setToken(r.data.token)

      // 获取可用客户列表
      const [err1, r1] = await client.listAvailableCustomer()
      if (err1) {
        handleError(err1)
        return
      }

      // 检查客户列表是否为空
      if (!r1.data || r1.data.length === 0) {
        this.$router.push('/noPermission')
        return
      }

      // 获取第一个客户的ID
      const customerId = r1.data[0].id

      // 更换token
      const [err2, r2] = await client.renewToken({
        body: {
          customerId: customerId
        }
      })
      if (err2) {
        handleError(err2)
        return
      }

      // 设置新的token
      setToken(r2.data.token)

      // 获取用户信息
      const [err3, r3] = await client.customerProfile()
      if (err3) {
        handleError(err3)
        return
      }
      setCustomerUserProfile(r3.data)

      //因为进入login页面时候没有加载菜单，导致跳转不过去
      await this.$parent.loadNavigations()
    },
    switchLoginType() {
      this.$router.push({ path: '/loginWithCaptcha' })
    },
    goToLogin() {
      // todo: Implement navigation if this is a separate view/component
      console.log('Navigate to Login')
    },
    goToRegister() {
      this.$router.push({ path: '/register' })
    },
    goToForgotPassword() {
      this.$router.push({ path: '/findPassword' })
    },
  }
}
</script>
