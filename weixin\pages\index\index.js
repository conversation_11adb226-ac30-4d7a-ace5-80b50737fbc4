import store from '../../utils/store.js';

Page({
  data: {
    // 保持 web-view 的 src 或其他页面数据
    webviewSrc: 'https://mp.weixin.qq.com/', 
  },

  onLoad() {
    // 页面加载时检查登录状态
    const token = store.getItem('token');

    if (!token) {
      // 如果 token 不存在，则没有登录，跳转到登录页面
      wx.reLaunch({
        url: '/pages/login/login',
      });
      // return; // 可以选择性地提前退出，防止后续代码执行
    }

    // 如果 token 存在，可以继续执行页面加载逻辑
    console.log('Token found, user is logged in.');
  }
});
