<template>
  <div class="menus">
    <div
      style="
        display: flex;
        align-items: center;
        border-bottom: 1px solid #eee;
        padding-left: 12px;
        height: 60px;
      "
    >
      <div style="flex: 1; margin-left: 5px">{{ navigation.title }}</div>
    </div>

    <div class="custom-menu">
      <template v-for="item in navigation.children">
        <!-- Case 1: Item is a parent group with children -->
        <div
          v-if="item.children && item.children.length > 0"
          :key="item.title"
          class="menu-group"
        >
          <div class="menu-parent-title">{{ item.title }}</div>
          <div
            v-for="subItem in item.children"
            :key="subItem.path"
            class="menu-child-item"
            :class="{ active: $route.path === subItem.path }"
            @click="navigateTo(subItem.path)"
          >
            {{ subItem.title }}
          </div>
        </div>

        <!-- Case 2: Item is a direct, top-level link (no children) -->
        <div
          v-else-if="item.path"
          :key="item.path"
          class="menu-top-item"
          :class="{ active: $route.path === item.path }"
          @click="navigateTo(item.path)"
        >
          {{ item.title }}
        </div>
      </template>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    navigation: {
      type: Object,
      default: () => ({ title: '', children: [] })
    }
  },
  methods: {
    navigateTo(path) {
      if (path && this.$route.path !== path) {
        this.$router.push(path)
      }
    }
  }
}
</script>

<style scoped>
.custom-menu {
  padding: 8px;
  user-select: none;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto,
    'Helvetica Neue', Arial, sans-serif;
}

.menu-group {
  margin-top: 16px;
}

.menu-parent-title {
  font-size: 14px;
  padding: 8px 12px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  color: #303133;
}

.menu-top-item,
.menu-child-item {
  font-size: 14px;
  padding: 12px 12px;
  cursor: pointer;
  border-radius: 6px;
  margin: 4px 0;

  transition: background-color 0.2s, color 0.2s;
}

.menu-child-item {
  padding-left: 32px; /* Increased indentation */
}

.menu-top-item:hover,
.menu-child-item:hover {
  background-color: var(--o-primary-bg-color);
}

.menu-top-item.active,
.menu-child-item.active {
  color: var(--o-primary-color);
  background-color: var(--o-primary-bg-color);
  font-weight: 400; /* Bolder font for active item */
}
</style>
