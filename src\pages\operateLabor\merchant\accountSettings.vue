<template>
  <div>
    <div class="user-info">
      <div
        style="
          width: 64px;
          height: 64px;
          text-align: center;
          background: #f2f2f2;
        "
      >
        <i
          class="el-icon-user-solid"
          style="color: #ccc; font-size: 48px; position: relative; top: 5px"
        />
      </div>
      <div class="user-details">
        <div>
          {{ userProfile.userPersonName || '未设置' }}
          <!-- <el-tag
            :type="userProfile.isPwd ? 'success' : 'warning'"
            size="small"
          >
            {{ userProfile.isPwd ? '已设置密码' : '未设置密码' }}
          </el-tag> -->
          <el-tag size="small" v-if="userProfile.admin" style="margin-left: 8px"
            >管理员</el-tag
          >
        </div>
        注册时间：{{ formatDate(userProfile.createTime) }}
      </div>
    </div>

    <div class="cards" style="margin-top: 20px; display: flex">
      <div
        class="card"
        style="
          background: #f8f8f8;
          padding: 20px;
          border-radius: 5px;
          width: 500px;
        "
      >
        <div
          style="
            display: flex;
            justify-content: space-between;
            align-items: center;
          "
        >
          <span style="font-size: 18px; color: #333; font-weight: 500">
            登录密码
          </span>
          <div>
            <el-tag
              :type="userProfile.isPwd ? 'success' : 'warning'"
              size="small"
            >
              {{ userProfile.isPwd ? '已设置' : '未设置' }}
            </el-tag>
            <span style="color: #ccc">|</span>
            <el-button type="text" @click="showPasswordDialog = true">
              {{ userProfile.isPwd ? '修改' : '设置' }}
            </el-button>
          </div>
        </div>
        <p style="font-size: 12px">
          安全性高的密码可以使账号更安全，建议您定期更换密码，设置一个包含字母、符号、数字6一20位的密码
        </p>
      </div>
    </div>

    <!-- 密码设置/修改对话框 -->
    <el-dialog
      :title="userProfile.isPwd ? '修改登录密码' : '设置登录密码'"
      :visible.sync="showPasswordDialog"
      width="500px"
      :close-on-click-modal="false"
    >
      <el-form
        ref="passwordForm"
        :model="passwordForm"
        :rules="passwordRules"
        label-width="120px"
      >
        <el-form-item prop="captcha" label="图形验证码">
          <Captcha v-model="passwordForm.captcha" ref="captcha" />
        </el-form-item>
        <el-form-item prop="code" label="短信验证码">
          <div style="position: relative">
            <el-input
              v-model="passwordForm.code"
              placeholder="请输入短信验证码"
            >
            </el-input>
            <el-button
              type="text"
              @click="sendCode"
              :disabled="isSendingCode"
              style="
                position: absolute;
                right: 10px;
                top: 50%;
                transform: translateY(-50%);
              "
            >
              {{ isSendingCode ? `${countdown}s` : '发送验证码' }}
            </el-button>
          </div>
        </el-form-item>
        <!-- 修改密码时需要输入原密码 -->
        <el-form-item
          v-if="userProfile.isPwd"
          label="原登录密码"
          prop="oldPassword"
        >
          <el-input
            v-model="passwordForm.oldPassword"
            type="password"
            placeholder="请输入原登录密码"
            show-password
          />
        </el-form-item>

        <!-- 新密码 -->
        <el-form-item
          :label="userProfile.isPwd ? '新登录密码' : '设置登录密码'"
          prop="password"
        >
          <el-input
            v-model="passwordForm.password"
            type="password"
            placeholder="设置6至20位新登录密码"
            show-password
          />
        </el-form-item>

        <!-- 确认密码 -->
        <el-form-item label="确认密码" prop="confirmPassword">
          <el-input
            v-model="passwordForm.confirmPassword"
            type="password"
            placeholder="请再次输入登录密码"
            show-password
          />
        </el-form-item>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button @click="cancelPasswordDialog">取消</el-button>
        <el-button type="primary" @click="submitPassword" :loading="submitting">
          确定
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import Captcha from './captcha.vue'
import { setCustomerUserProfile, getCustomerUserProfile } from './context'
import makeClient from '../../../services/operateLabor/makeClient'
import handleError from '../../../helpers/handleError'
const client = makeClient()
const COUNTDOWN_SECONDS = 60

export default {
  components: {
    Captcha
  },
  name: 'AccountSettings',

  data() {
    return {
      userProfile: {},
      showPasswordDialog: false,
      submitting: false,
      passwordForm: {
        oldPassword: '',
        password: '',
        confirmPassword: '',
        code: '',
        otpToken: '',
        passwordMatch: true,
        captcha: {
          token: '',
          value: ''
        }
      },
      passwordRules: {
        code: [
          { required: true, message: '请输入短信验证码', trigger: 'blur' }
        ],
        oldPassword: [
          { required: true, message: '请输入原登录密码', trigger: 'blur' }
        ],
        password: [
          { required: true, message: '请输入新密码', trigger: 'blur' },
          { min: 6, max: 20, message: '密码长度为6-20位', trigger: 'blur' }
        ],
        confirmPassword: [
          { required: true, message: '请确认密码', trigger: 'blur' },
          { validator: this.validateConfirmPassword, trigger: 'blur' }
        ]
      },
      isSendingCode: false,
      countdown: COUNTDOWN_SECONDS,
      timer: null
    }
  },

  created() {
    this.loadUserProfile()
  },

  methods: {
    async loadUserProfile() {
      const [err, r] = await client.customerProfile()
      if (err) {
        handleError(err)
        return
      }
      this.userProfile = r.data
    },

    formatDate(dateString) {
      if (!dateString) return '未设置'
      return new Date(dateString).toLocaleString('zh-CN')
    },

    validateConfirmPassword(rule, value, callback) {
      if (value !== this.passwordForm.password) {
        callback(new Error('两次输入的密码不一致'))
      } else {
        callback()
      }
    },

    cancelPasswordDialog() {
      this.showPasswordDialog = false
      this.resetPasswordForm()
    },

    resetPasswordForm() {
      this.passwordForm = {
        oldPassword: '',
        password: '',
        confirmPassword: '',
        code: '',
        otpToken: '',
        passwordMatch: true
      }
      this.$nextTick(() => {
        this.$refs.passwordForm && this.$refs.passwordForm.clearValidate()
      })
    },

    async submitPassword() {
      this.$refs.passwordForm.validate(async valid => {
        if (!valid) return

        this.submitting = true

        try {
          if (this.userProfile.isPwd) {
            // 修改密码
            await this.resetPassword()
          } else {
            // 首次设置密码
            await this.setPassword()
          }

          this.$message.success(
            this.userProfile.isPwd ? '密码修改成功' : '密码设置成功'
          )
          this.showPasswordDialog = false
          this.resetPasswordForm()

          // 更新用户信息
          this.userProfile.isPwd = true
        } catch (error) {
          console.error('密码操作失败:', error)
        } finally {
          this.submitting = false
        }
      })
    },

    async setPassword() {
      const [err] = await client.customerSetPassword({
        body: {
          code: this.passwordForm.code,
          otpToken: this.passwordForm.otpToken,
          password: this.passwordForm.password,
          confirmPassword: this.passwordForm.confirmPassword,
          passwordMatch: this.passwordForm.passwordMatch
        }
      })

      if (err) {
        handleError(err)
        throw new Error(err.message || '设置密码失败')
      }
    },

    async resetPassword() {
      const [err] = await client.customerResetPassword({
        body: {
          oldPassword: this.passwordForm.oldPassword,
          password: this.passwordForm.password,
          confirmPassword: this.passwordForm.confirmPassword,
          passwordMatch: this.passwordForm.passwordMatch
        }
      })

      if (err) {
        handleError(err)
        throw new Error(err.message || '修改密码失败')
      }
    },

    async sendCode() {
      if (!this.passwordForm.captcha.value) {
        this.$message.error('请输入图形验证码')
        return
      }
      const [err, r] = await client.customerSendOtp({
        body: {
          captchaToken: this.passwordForm.captcha.token,
          captchaAnswer: this.passwordForm.captcha.value,
          businessType: 'OLD_land_diy'
        }
      })

      if (err) {
        handleError(err)
        this.$refs.captcha.refreshCaptcha()
        return
      }
      this.passwordForm.otpToken = r.data.token

      this.startCountdown()
    },
    startCountdown() {
      const endTime = Date.now() + COUNTDOWN_SECONDS * 1000
      localStorage.setItem('smsCountdownEndTime', endTime)
      this.isSendingCode = true

      this.timer = setInterval(() => {
        const now = Date.now()
        const remaining = Math.round((endTime - now) / 1000)

        if (remaining <= 0) {
          clearInterval(this.timer)
          this.isSendingCode = false
          this.countdown = COUNTDOWN_SECONDS
          localStorage.removeItem('smsCountdownEndTime')
        } else {
          this.countdown = remaining
        }
      }, 1000)
    }
  }
}
</script>

<style scoped>
.account-settings {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
}

.page-header {
  margin-bottom: 24px;
}

.page-header h2 {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: #303133;
}

.user-info-card {
  margin-bottom: 24px;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 20px;
}

.user-details h3 {
  margin: 0 0 8px 0;
  font-size: 18px;
  font-weight: 600;
  color: #303133;
}

.user-details p {
  margin: 4px 0;
  color: #606266;
  font-size: 14px;
}

.password-card {
  margin-bottom: 24px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.password-info {
  padding: 16px 0;
}

.password-status {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
  font-size: 14px;
  font-weight: 500;
}

.password-desc {
  margin: 0;
  color: #909399;
  font-size: 12px;
  line-height: 1.5;
}

.dialog-footer {
  text-align: right;
}

.dialog-footer .el-button {
  margin-left: 10px;
}

::v-deep .el-card__header {
  padding: 18px 20px;
  border-bottom: 1px solid #ebeef5;
}

::v-deep .el-card__body {
  padding: 20px;
}

::v-deep .el-form-item {
  margin-bottom: 22px;
}

::v-deep .el-form-item__label {
  font-weight: 500;
}
</style>
