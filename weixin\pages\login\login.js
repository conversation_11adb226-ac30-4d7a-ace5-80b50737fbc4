Page({
  data: {
    domainInfo: {
      logoUrl: 'https://olading.com/static/images/oladLogo.png',
      captchaUrl:
        'https://olading.com/api/merchant/platform/captcha?token=ehNoX0JgPfW%2FO%2FBXKWm31UB%2Bjn%2Fw79mQiSeU2FgpoUCGDBBBKUHgbR6mit42TKig'
    },
    countdown: 0
  },

  onLoad() {
    // 在这里可以从远程API加载domainInfo
  },

  getVerificationCode() {
    this.setData({ countdown: 60 })
    const interval = setInterval(() => {
      if (this.data.countdown > 0) {
        this.setData({ countdown: this.data.countdown - 1 })
      } else {
        clearInterval(interval)
      }
    }, 1000)
  },

  login() {
    // 在这里处理登录逻辑
    // 登录成功后
    wx.setStorageSync('token', 'your_token_here')
    wx.reLaunch({
      url: '/pages/index/index'
    })
  }
})
