import store from '../../utils/store.js'
import handleError from '../../utils/handleError.js'
import { setToken } from '../../utils/token.js'
import makeClient from '../../utils/makeClient.js'

const client = makeClient()

Page({
  data: {
    domainInfo: null,
    countdown: 0,
    phone: '',
    code: ''
  },

  async onLoad() {
    try {
      var domainInfo = store.getItem('domainInfo')
      if (!domainInfo) {
        domainInfo = {
          domainName: '156-dev.olading.com'
        }
      }

      const [err, r] = await client.domainInfo({
        body: {
          domain: domainInfo.domainName
        }
      })
      if (err) {
        handleError(err)
        return
      }

      this.setData({ domainInfo: r.data.data })
      store.setItem('domainInfo', r.data.data)
    } catch (e) {
      console.error(e)
    }
  },

  // 绑定输入框
  onPhoneInput(e) {
    this.setData({ phone: e.detail.value })
  },
  onCodeInput(e) {
    this.setData({ code: e.detail.value })
  },

  getVerificationCode() {
    // ... (您的获取验证码逻辑保持不变)
    this.setData({ countdown: 60 })
    const interval = setInterval(() => {
      if (this.data.countdown > 0) {
        this.setData({ countdown: this.data.countdown - 1 })
      } else {
        clearInterval(interval)
      }
    }, 1000)
  },

  async login() {
    const { phone, code } = this.data
    if (!phone || !code) {
      wx.showToast({ title: '请输入手机号和验证码', icon: 'none' })
      return
    }

    wx.showLoading({ title: '登录中...' })

    // 使用 client.request 发起登录请求
    const [err, res] = await client.request('/api/login', {
      // 请替换为您的真实登录API路径
      method: 'POST',
      body: {
        phone: phone,
        code: code
      }
    })

    wx.hideLoading()

    if (err) {
      handleError(err) // 使用您已有的错误处理器
      return
    }

    // 假设 res.data 中包含了 token
    if (res && res.data && res.data.token) {
      setToken(res.data.token) // 使用您已有的 token 工具
      wx.reLaunch({ url: '/pages/index/index' })
    } else {
      // 处理业务错误，例如密码错误
      handleError({
        message: (res && res.data && res.data.message) || '登录失败'
      })
    }
  }
})
