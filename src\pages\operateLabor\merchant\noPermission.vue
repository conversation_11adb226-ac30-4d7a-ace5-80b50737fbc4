<template>
  <div class="no-permission-container">
    <div class="no-permission-content">
      <div class="title-container">
        <i class="el-icon-warning-outline warning-icon"></i>
        <h2>无访问权限</h2>
      </div>
      <p>您当前没有访问任何菜单的权限，请联系管理员授权。</p>
    </div>
  </div>
</template>

<script>
export default {
  name: 'NoPermission'
}
</script>

<style scoped>
.no-permission-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  width: 100%;
}

.no-permission-content {
  text-align: center;
  padding: 180px;
}

.title-container {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 20px;
}

.warning-icon {
  font-size: 24px;
  color: #E6A23C;
  margin-right: 8px;
}

h2 {
  font-size: 24px;
  color: #303133;
  margin: 0;
}

p {
  font-size: 16px;
  color: #606266;
}
</style>