<template>
  <div :style="{ display: 'flex' }" v-if="captcha.token">
    <el-input
      v-model="captcha.value"
      placeholder="请输入图形验证码"
      :style="{ flex: 1, marginRight: '10px' }"
      @change="emitToken"
    ></el-input>
    <img @click="refreshCaptcha" :src="captchaImage" alt="captcha" />
  </div>
</template>

<script>
import handleError from 'kit/helpers/handleError'
import makeClient from '../../../services/operateLabor/makeClient'
const client = makeClient()

export default {
  name: 'Captcha',
  computed: {
    captchaImage() {
      return `${
        window.env?.apiPath
      }/api/public/captcha?token=${encodeURIComponent(this.captcha.token)}`
    }
  },
  data() {
    return {
      captcha: {
        token: '',
        value: ''
      }
    }
  },
  mounted() {
    this.refreshCaptcha()
  },
  methods: {
    async refreshCaptcha() {
      const [err, r] = await client.createCaptcha()
      if (err) {
        handleError(err)
        return
      }

      this.captcha.token = r.data
      this.emitToken()
    },
    emitToken() {
      this.$emit('input', this.captcha)
    }
  }
}
</script>
