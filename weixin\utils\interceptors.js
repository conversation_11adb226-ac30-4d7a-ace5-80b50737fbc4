export const gatewayInterceptor = gateway => {
  return function (resource, options) {
    resource = `${gateway}${resource}`
    return [null, resource, options]
  }
}

export const requestDefaultHeadersInterceptor = () => {
  return function (resource, options) {
    options.method = options.method || 'POST'
    options.headers['Content-Type'] =
      options.headers['Content-Type'] || 'application/json'

    return [null, resource, options]
  }
}

export const jsonResponseInterceptor = () => {
  return async function (resource, options, result) {
    if (result.statusCode !== 200) {
      return [result.errMsg, null]
    }

    if (options.headers['content-type'] === 'application/octet-stream') {
      return [null, result]
    }
    var err = null
    var r = null

    r = result
    r.data.errorCode = parseInt(r.data.errorCode, 10)

    if (!r.data.success) {
      err = {
        errorCode: r.data.errorCode,
        message: r.data.message || '内部错误'
      }

      return [err, null]
    }

    return [null, r]
  }
}
