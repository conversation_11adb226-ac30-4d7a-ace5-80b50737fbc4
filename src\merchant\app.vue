<template>
  <div class="box">
    <div style="width: 100%" v-if="$route.meta.noNavigationsAndMenus">
      <router-view />
    </div>

    <div
      style="
        height: 100vh;
        display: flex;
        align-items: center;
        justify-content: center;
      "
      v-if="!$route.meta.noNavigationsAndMenus && loading"
    >
      <div style="font-size: 12px">
        <i class="el-icon-loading" style="color: var(--o-primary-color)"></i>
        加载中
      </div>
    </div>

    <Navigations
      v-if="!$route.meta.noNavigationsAndMenus && !loading"
      :navigations="navigations"
      @change="goNavigation"
      :logoURL="logoURL"
    />
    <div style="display: flex; height: calc(100vh - 65px)" v-if="!loading">
      <Menus
        style="flex: 0 0 240px; border-right: 1px solid #eee"
        v-if="!$route.meta.noNavigationsAndMenus"
        :navigation="selectedNavigation"
      />
      <div
        style="overflow-y: auto; width: 100%; background: #f7f7f7"
        v-if="!$route.meta.noNavigationsAndMenus"
      >
        <div
          style="
            margin: 14px 0 0 14px;
            padding: 12px 24px;
            background: #fff;
            font-size: 16px;
            font-weight: 400;
          "
        >
          <span
            type="link"
            v-if="!$route.meta.noNeedBack"
            style="
              color: var(--o-primary-color);
              cursor: pointer;
              margin-right: 12px;
            "
            @click="$router.back()"
          >
            返回
          </span>
          {{ $route.meta.title }}
        </div>
        <div
          style="
            flex: 1;
            margin: 1px 0 0 14px;
            padding: 12px 24px;
            background: #fff;
            height: calc(100vh - 151px);
            overflow: hidden;
            overflow-y: auto;
          "
        >
          <router-view />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import Menus from './menus.vue'
import Navigations from './navigations.vue'
import makeClient from 'kit/services/operateLabor/makeClient'
import handleError from 'kit/helpers/handleError'
const client = makeClient()

export default {
  components: {
    Menus,
    Navigations
  },
  data() {
    return {
      loading: true,
      selectedNavigation: null,
      navigations: [],
      logoURL: ''
    }
  },
  async created() {
    if (this.$route.meta.noNavigationsAndMenus) return
    await this.loadNavigations()
  },
  mounted() {
    console.log(this.$route.meta)
  },
  methods: {
    goNavigation(navigation) {
      this.selectedNavigation = navigation
      if (this.isCurrentNavigation(navigation)) return
      const path = this.findFirstPathOfNavigation(navigation)
      this.$router.push(path)
    },
    isCurrentNavigation(navigation) {
      const currentPath = this.$route.path
      for (var c of navigation.children) {
        if (c.path === currentPath) return true
        if (c.children && c.children.length) {
          for (var cc of c.children) {
            if (cc.path === currentPath) return true
          }
        }
      }

      return false
    },
    findFirstPathOfNavigation(navigation) {
      if (!navigation.children) return
      for (var c of navigation.children) {
        if (c.path) return c.path
        if (c.children) {
          for (var cc of c.children) {
            if (cc.path) return cc.path
          }
        }
      }
    },
    async loadNavigations() {
      this.loading = true
      const [err, r] = await client.merchantGetMenu()
      this.loading = false
      if (err) {
        handleError(err)
        return
      }

      this.navigations = r.data.children
      // 如果菜单列表为空，跳转到无权限页面
      if (!this.navigations || this.navigations.length === 0) {
        this.$router.push('/noPermission')
        return
      }

      this.selectedNavigation = this.navigations[0]
      const domainInfo = JSON.parse(localStorage.getItem('domainInfo'))
      this.logoURL = domainInfo?.logoUrl

      const shouldAutoNavigate = this.$route.path === '/' ||
                                 this.$route.path === '' ||
                                 !sessionStorage.getItem('mainNavActiveIndex')

      if (shouldAutoNavigate) {
        // 查找所有导航中的第一个有效路径并跳转
        var firstPath = this.findFirstPathInAllNavigations()
        if (firstPath) {
          this.$router.push(firstPath)
        } else {
          // 如果没有找到有效路径，跳转到无权限页面
          this.$router.push('/noPermission')
        }
      }
    },

    findFirstPathInAllNavigations() {
      // 遍历所有导航，查找第一个有 path 属性的菜单项
      for (const navigation of this.navigations) {
        if (navigation.path) return navigation.path

        if (navigation.children && navigation.children.length) {
          for (const child of navigation.children) {
            if (child.path) return child.path

            if (child.children && child.children.length) {
              for (const grandChild of child.children) {
                if (grandChild.path) return grandChild.path
              }
            }
          }
        }
      }

      return null
    }
  }
}
</script>

<style></style>
