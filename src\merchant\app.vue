<template>
  <div class="box">
    <div style="width: 100%" v-if="$route.meta.noNavigationsAndMenus">
      <router-view />
    </div>

    <div
      style="
        height: 100vh;
        display: flex;
        align-items: center;
        justify-content: center;
      "
      v-if="!$route.meta.noNavigationsAndMenus && loading"
    >
      <div style="font-size: 12px">
        <i class="el-icon-loading" style="color: var(--o-primary-color)"></i>
        加载中
      </div>
    </div>

    <div style="display: flex; height: 100vh" v-if="!loading && !$route.meta.noNavigationsAndMenus">
      <div style="overflow-y: auto; width: 100%; background: #f7f7f7">
        <div
          style="
            margin: 14px 0 0 14px;
            padding: 12px 24px;
            background: #fff;
            font-size: 16px;
            font-weight: 400;
          "
        >
          <span
            type="link"
            v-if="!$route.meta.noNeedBack"
            style="
              color: var(--o-primary-color);
              cursor: pointer;
              margin-right: 12px;
            "
            @click="$router.back()"
          >
            返回
          </span>
          {{ $route.meta.title }}
        </div>
        <div
          style="
            flex: 1;
            margin: 1px 0 0 14px;
            padding: 12px 24px;
            background: #fff;
            height: calc(100vh - 151px);
            overflow: hidden;
            overflow-y: auto;
          "
        >
          <router-view />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import makeClient from 'kit/services/operateLabor/makeClient'
import handleError from 'kit/helpers/handleError'
const client = makeClient()

export default {
  data() {
    return {
      loading: false
    }
  },
  async created() {
    if (this.$route.meta.noNavigationsAndMenus) return
    await this.loadNavigations()
  },
  methods: {
    async loadNavigations() {
      this.loading = true
      // 这里可以根据需要加载merchant相关的导航菜单
      // 暂时简化处理
      this.loading = false
    }
  }
}
</script>

<style></style>
