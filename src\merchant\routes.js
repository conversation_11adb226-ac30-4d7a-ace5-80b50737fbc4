import Index from 'kit/pages/operateLabor/merchant/index.vue'
import Login from 'kit/pages/operateLabor/merchant/login.vue'
import LoginWithCaptcha from 'kit/pages/operateLabor/merchant/loginWithCaptcha.vue'
import AccountSettings from 'kit/pages/operateLabor/merchant/accountSettings.vue'

// 业务管理
import ServiceContracts from 'kit/pages/operateLabor/merchant/serviceContracts.vue'

// 任务管理
import TaskInfo from 'kit/pages/operateLabor/merchant/taskInfo.vue'

// 人员管理
import LaborInfo from 'kit/pages/operateLabor/merchant/laborInfo.vue'

// 结算管理
import SalaryCalculate from 'kit/pages/operateLabor/merchant/salaryCalculate.vue'
import BillingManage from 'kit/pages/operateLabor/merchant/billingManage.vue'
import Invoices from 'kit/pages/operateLabor/merchant/invoices.vue'

// 设置
import Roles from 'kit/pages/operateLabor/merchant/roles.vue'

const routes = [
  {
    path: '/',
    component: Index,
    meta: {
      title: '首页'
    }
  },
  {
    path: '/login',
    component: Login,
    meta: {
      title: '登录',
      noNavigationsAndMenus: true
    }
  },
  {
    path: '/loginWithCaptcha',
    component: LoginWithCaptcha,
    meta: {
      title: '验证码登录',
      noNavigationsAndMenus: true
    }
  },
  {
    path: '/accountSettings',
    component: AccountSettings,
    meta: {
      title: '账户管理'
    }
  },
  // 业务管理
  {
    path: '/serviceContracts',
    component: ServiceContracts,
    meta: {
      title: '合同管理',
      noNeedBack: true
    }
  },
  // 任务管理
  {
    path: '/taskInfo',
    component: TaskInfo,
    meta: {
      title: '任务信息',
      noNeedBack: true
    }
  },
  // 人员管理
  {
    path: '/laborInfo',
    component: LaborInfo,
    meta: {
      title: '人员信息',
      noNeedBack: true
    }
  },
  // 结算管理
  {
    path: '/salaryCalculate',
    component: SalaryCalculate,
    meta: {
      title: '薪酬计算',
      noNeedBack: true
    }
  },
  {
    path: '/billingManage',
    component: BillingManage,
    meta: {
      title: '账单管理',
      noNeedBack: true
    }
  },
  {
    path: '/invoices',
    component: Invoices,
    meta: {
      title: '发票管理',
      noNeedBack: true
    }
  },
  // 设置
  {
    path: '/roles',
    component: Roles,
    meta: {
      title: '权限管理',
      noNeedBack: true
    }
  }
]

export default routes