import Index from 'kit/pages/operateLabor/merchant/index.vue'
import Login from 'kit/pages/operateLabor/merchant/login.vue'
import LoginWithCaptcha from 'kit/pages/operateLabor/merchant/loginWithCaptcha.vue'
import AccountSettings from 'kit/pages/operateLabor/merchant/accountSettings.vue'

const routes = [
  {
    path: '/',
    component: Index,
    meta: {
      title: '首页'
    }
  },
  {
    path: '/login',
    component: Login,
    meta: {
      title: '登录',
      noNavigationsAndMenus: true
    }
  },
  {
    path: '/loginWithCaptcha',
    component: LoginWithCaptcha,
    meta: {
      title: '验证码登录',
      noNavigationsAndMenus: true
    }
  },
  {
    path: '/accountSettings',
    component: AccountSettings,
    meta: {
      title: '账户管理'
    }
  }
]

export default routes